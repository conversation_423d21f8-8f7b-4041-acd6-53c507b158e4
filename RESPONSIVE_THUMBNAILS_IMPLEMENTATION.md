# Implementação de Galeria Responsiva por Breakpoint

## Resumo
Esta implementação adiciona a funcionalidade de configurar diferentes números de thumbnails E imagens principais visíveis para cada breakpoint de dispositivo no plugin Woo Variation Gallery.

## Modificações Realizadas

### 1. Configurações de Admin (class-woo-variation-gallery-settings.php)
- **Nova seção "Main Images Options":**
  - `main_images_to_show`: Imagens principais para desktop - padrão: 1
  - `medium_device_main_images_to_show`: Imagens principais para dispositivos médios (≤992px) - padrão: 1
  - `small_device_main_images_to_show`: Imagens principais para tablets (≤768px) - padrão: 1
  - `extra_small_device_main_images_to_show`: Imagens principais para phones (≤480px) - padrão: 1

- **Configurações de thumbnails expandidas:**
  - `medium_device_thumbnails_columns`: Thumbnails para dispositivos médios (≤992px) - padrão: 3
  - `small_device_thumbnails_columns`: Thumbnails para tablets (≤768px) - padrão: 3
  - `extra_small_device_thumbnails_columns`: Thumbnails para phones (≤480px) - padrão: 2

- **Modificação da configuração existente:**
  - `thumbnails_columns`: Agora especificamente para desktop com ícone de desktop

### 2. Frontend JavaScript (class-woo-variation-gallery-frontend.php)
- **Adicionadas novas variáveis JavaScript para thumbnails:**
  - `gallery_medium_device_thumbnails_columns`
  - `gallery_small_device_thumbnails_columns`
  - `gallery_extra_small_device_thumbnails_columns`

- **Adicionadas novas variáveis JavaScript para imagens principais:**
  - `gallery_main_images_to_show`
  - `gallery_medium_device_main_images_to_show`
  - `gallery_small_device_main_images_to_show`
  - `gallery_extra_small_device_main_images_to_show`

- **Modificação do método add_inline_style():**
  - Carregamento das novas configurações por breakpoint
  - Passagem das variáveis para o arquivo stylesheet.php

### 3. Template de Imagens (templates/product-images.php)
- **Configuração responsiva do Slider Principal:**
  - `slidesToShow`: Configurado por breakpoint para imagens principais
  - Breakpoint 992px: Usa `medium_device_main_images`
  - Breakpoint 768px: Usa `small_device_main_images`
  - Breakpoint 480px: Usa `extra_small_device_main_images`
  - Fade desabilitado quando múltiplas imagens são mostradas

- **Configuração responsiva do Slider de Thumbnails:**
  - Breakpoint 992px: Usa `medium_device_columns`
  - Breakpoint 768px: Usa `small_device_columns`
  - Breakpoint 480px: Usa `extra_small_device_columns`

- **Configurações aplicadas em cada breakpoint:**
  - `slidesToShow`: Número de imagens/thumbnails visíveis
  - `slidesToScroll`: Número de thumbnails a rolar
  - Mantém configurações de orientação vertical/horizontal existentes

### 4. CSS Responsivo (includes/stylesheet.php)
- **Variáveis CSS responsivas para thumbnails:**
  - `--wvg-thumbnail-item` atualizada para cada breakpoint

- **Variáveis CSS responsivas para imagens principais:**
  - `--wvg-main-images-to-show` atualizada para cada breakpoint

- **Media queries para 992px, 768px e 480px**

- **CSS adicional para múltiplas imagens principais:**
  - Espaçamento entre imagens principais quando múltiplas são mostradas
  - Ajustes de margem para layout responsivo

- **Documentação atualizada:**
  - Adicionadas as novas variáveis no cabeçalho do arquivo

## Breakpoints Configurados

### Imagens Principais
| Dispositivo | Breakpoint | Imagens Padrão | Configuração | Limite |
|-------------|------------|----------------|--------------|--------|
| Desktop | > 992px | 1 | `main_images_to_show` | 1-4 |
| Medium Devices | ≤ 992px | 1 | `medium_device_main_images_to_show` | 1-4 |
| Small Devices/Tablets | ≤ 768px | 1 | `small_device_main_images_to_show` | 1-3 |
| Extra Small/Phones | ≤ 480px | 1 | `extra_small_device_main_images_to_show` | 1-2 |

### Thumbnails
| Dispositivo | Breakpoint | Thumbnails Padrão | Configuração |
|-------------|------------|-------------------|--------------|
| Desktop | > 992px | 4 | `thumbnails_columns` |
| Medium Devices | ≤ 992px | 3 | `medium_device_thumbnails_columns` |
| Small Devices/Tablets | ≤ 768px | 3 | `small_device_thumbnails_columns` |
| Extra Small/Phones | ≤ 480px | 2 | `extra_small_device_thumbnails_columns` |

## Como Usar

### Para Administradores:
1. Acesse WooCommerce → Settings → Variation Gallery
2. Na seção "Main Images Options", configure:
   - **Main Images to Show** (Desktop): 1-4 imagens principais
   - **Main Images to Show** (Medium): 1-4 imagens principais
   - **Main Images to Show** (Small): 1-3 imagens principais
   - **Main Images to Show** (Extra Small): 1-2 imagens principais
3. Na seção "Thumbnail Options", configure:
   - **Thumbnails Item** (Desktop): 2-8 thumbnails
   - **Thumbnails Item** (Medium): 2-8 thumbnails
   - **Thumbnails Item** (Small): 2-8 thumbnails
   - **Thumbnails Item** (Extra Small): 2-8 thumbnails

### Para Desenvolvedores:
```php
// Filtros disponíveis para personalização de imagens principais
add_filter( 'woo_variation_gallery_default_main_images', function() { return 1; } );
add_filter( 'woo_variation_gallery_medium_device_main_images', function() { return 1; } );
add_filter( 'woo_variation_gallery_small_device_main_images', function() { return 1; } );
add_filter( 'woo_variation_gallery_extra_small_device_main_images', function() { return 1; } );

// Filtros disponíveis para personalização de thumbnails
add_filter( 'woo_variation_gallery_default_thumbnails_columns', function() { return 4; } );
add_filter( 'woo_variation_gallery_medium_device_thumbnails_columns', function() { return 3; } );
add_filter( 'woo_variation_gallery_small_device_thumbnails_columns', function() { return 3; } );
add_filter( 'woo_variation_gallery_extra_small_device_thumbnails_columns', function() { return 2; } );
```

## Compatibilidade
- ✅ Mantém compatibilidade com configurações existentes
- ✅ Funciona com orientação vertical/horizontal de thumbnails
- ✅ Suporta RTL (Right-to-Left)
- ✅ Integra com sistema de configurações existente do plugin

## Arquivos Modificados
1. `includes/class-woo-variation-gallery-settings.php`
2. `includes/class-woo-variation-gallery-frontend.php`
3. `templates/product-images.php`
4. `includes/stylesheet.php`

## Testes Recomendados
1. Verificar configurações no admin do WordPress
2. Testar responsividade em diferentes tamanhos de tela
3. Verificar funcionamento com produtos variáveis
4. Testar orientação vertical e horizontal de thumbnails
5. Verificar compatibilidade com temas diferentes
