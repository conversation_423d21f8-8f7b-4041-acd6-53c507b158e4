<?php
defined( 'ABSPATH' ) or die( 'Keep Quit' );
?>

<h2>
	<?php esc_html_e( 'How to tutorials', 'woo-variation-gallery' ); ?>
</h2>

<div id="woo-variation-gallery-tutorials-wrapper">

	<ul>
		<li>
			<div class="tutorial-image-wrapper">
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-1.png' ) ?>"></div>
			<div class="tutorial-description-wrapper">
				<h3>Adjust Variation Gallery Width For Desktop, Tablet, Mobile</h3>
				<div class="tutorial-contents">
					To fit your variation image gallery throughout viewing devices, It offers width control for Desktop, Tablet, and Mobile.
				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/adjustable-gallery-width-for-desktop-tablet-mobile/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#fix-small-gallery-issue-desktop-tablet-mobile" target="_blank" class="button button-docs">Documentation</a>
				</div>
			</div>
		</li>

		<li>
			<div class="tutorial-description-wrapper">
				<h3>Multiple Image Per Product Variation</h3>
				<div class="tutorial-contents">
					With default WooCommerce, you can insert only a single image additionally. WooCommerce Additional Variation Images Gallery plugin brings an option to insert unlimited images for each WooCommerce product variation.
				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/insert-unlimited-variation-images/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-images-in-the-variation-gallery" target="_blank" class="button button-docs">Documentation</a>
				</div>
			</div>
			<div class="tutorial-image-wrapper">
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-2.png' ) ?>"></div>
		</li>

		<li>
			<div class="tutorial-image-wrapper">
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-6.png' ) ?>"></div>
			<div class="tutorial-description-wrapper">
				<h3>Left, Right, and Bottom Gallery Thumbnail Display Control</h3>
				<div class="tutorial-contents">
					Default WooCommerce comes with a product variation gallery with a bottom thumbnail feature. WooCommerce Extra Variation Images Gallery plugin has also extended the feature. It allows you to control the gallery thumbnail position to left, right, and bottom.

				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/thumbnail-display-position/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#fix-small-gallery-issue-desktop-tablet-mobile" target="_blank" class="button button-docs">Documentation</a>
				</div>
			</div>
		</li>

		<li>
			<div class="tutorial-description-wrapper">
				<h3>Enable Thumbnail Slide</h3>
				<div class="tutorial-contents">
					Variable product attribute variation image gallery can be displayed in slider or show all thumbnails picture in grid one after another. With this plugin, you can go for either feature you want.
				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/enable-thumbnail-slide/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-images-in-the-variation-gallery" target="_blank" class="button button-docs">Documentation</a>
				</div>
			</div>
			<div class="tutorial-image-wrapper">
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-7.png' ) ?>"></div>
		</li>

		<li>
			<div class="tutorial-image-wrapper">
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-8.png' ) ?>"></div>
			<div class="tutorial-description-wrapper">
				<h3>Thumbnails Item Number and Gap Control</h3>
				<div class="tutorial-contents">
					Thumbnails are one of the crucial features of the smart variations images gallery plugin. It allows specifying how many pictures you want to show in a single thumbnail slider from 4 to 8. Furthermore, it let to specify gaps between thumbnails.

				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/thumbnails-item-number-and-gap-control/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#fix-small-gallery-issue-desktop-tablet-mobile" target="_blank" class="button button-docs">Documentation</a>
				</div>
			</div>
		</li>

		<li>
			<div class="tutorial-description-wrapper">
				<h3>Slider and Thumbnail Slider Arrow Control</h3>
				<div class="tutorial-contents">
					The WooCommerce variation images gallery plugin consists of two parts, the main image part, and the thumbnail part. Both parts come with a slider with the option to enable and disable image sliding slideshow indicating arrow.
				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/main-slider-and-thumbnail-slider-arrow-control/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-images-in-the-variation-gallery" target="_blank" class="button button-docs">Documentation</a>
				</div>
			</div>
			<div class="tutorial-image-wrapper">
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-9.png' ) ?>"></div>
		</li>

		<li>
			<div class="tutorial-image-wrapper">
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-10.gif' ) ?>"></div>
			<div class="tutorial-description-wrapper">
				<h3>Product Variation Image Zoom</h3>
				<div class="tutorial-contents">
					Zoom is on of the important feature high-converting product image gallery. To boost your sales and conversion, WooCommerce Extra Variation Images Gallery plugin ships packed with the product featured image zoom option. You can disable the zoom option on your need.

				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/image-zoom/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#fix-small-gallery-issue-desktop-tablet-mobile" target="_blank" class="button button-docs">Documentation</a>
				</div>
			</div>
		</li>

		<li>
			<div class="tutorial-description-wrapper">
				<h3>Popup Icon Display Position</h3>
				<div class="tutorial-contents">
					If you have already installed WooCommerce Additional Variation Images plugin activated, you can notice it shows a zoom icon at the right top corner of the screen by default. You can change its position from the plugin settings.
				</div>
				<div class="tutorial-buttons">
					<a href="hhttps://demo.getwooplugins.com/woocommerce-variation-gallery/image-popup-icon-display-position/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-images-in-the-variation-gallery" target="_blank" class="button button-docs">Documentation</a>
				</div>
			</div>
			<div class="tutorial-image-wrapper">
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-11.gif' ) ?>"></div>
		</li>

		<li>
			<div class="tutorial-image-wrapper">
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-11.png' ) ?>"></div>
			<div class="tutorial-description-wrapper">
				<h3>Variation Image Shorting Option</h3>
				<div class="tutorial-contents">
					The sorting option is an incredible port of this WooCommerce Additional Variation Images Gallery plugin, it allows you to reorder variation images after uploading them. It means you can decide the order of the variation image display in the gallery.

				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/variation-image-shorting-option/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#fix-small-gallery-issue-desktop-tablet-mobile" target="_blank" class="button button-docs">Documentation</a>
				</div>
			</div>
		</li>

		<li>

			<div class="tutorial-description-wrapper">
				<h3>Multiple Image Selection While uploading Variation Images from Media Gallery</h3>
				<div class="tutorial-contents">
					When you insert images for your product variation image gallery, it allows selecting unlimited images each time. Say goodbye to one image selection for each time.
				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/upload-bulk-images-inside-variations/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-youtube-video-in-the-product-variation-gallery" target="_blank" class="button button-docs">Documentation</a>
					<?php if ( ! woo_variation_gallery()->is_pro() ): ?>
						<a href="<?php echo esc_url( woo_variation_gallery()->get_backend()->get_pro_link() ) ?>" target="_blank" class="button button-pro">Upgrade to pro</a>
					<?php endif; ?>
				</div>
			</div>
			<div class="tutorial-image-wrapper">
				<?php if ( ! woo_variation_gallery()->is_pro() ): ?>
					<div class="ribbon"><span><?php esc_html_e( 'PRO', 'woo-variation-gallery' ) ?></span></div>
				<?php endif; ?>
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-12.png' ) ?>">
			</div>
		</li>

		<li>
			<div class="tutorial-image-wrapper">
				<?php if ( ! woo_variation_gallery()->is_pro() ): ?>
					<div class="ribbon"><span><?php esc_html_e( 'PRO', 'woo-variation-gallery' ) ?></span></div>
				<?php endif; ?>
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-3.png' ) ?>">
			</div>
			<div class="tutorial-description-wrapper">
				<h3>Display YouTube, Vimeo, Hosted Video Per Product Variation</h3>
				<div class="tutorial-contents">
					Besides adding extra images per WooCommerce product variation for product image gallery, with this plugin, you can insert unlimited YouTube, Vimeo and Self Hosted videos with ease.
				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/insert-self-hosted-video-in-product-variations/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-youtube-video-in-the-product-variation-gallery" target="_blank" class="button button-docs">Documentation</a>
					<?php if ( ! woo_variation_gallery()->is_pro() ): ?>
						<a href="<?php echo esc_url( woo_variation_gallery()->get_backend()->get_pro_link() ) ?>" target="_blank" class="button button-pro">Upgrade to pro</a>
					<?php endif; ?>
				</div>
			</div>

		</li>

		<li>

			<div class="tutorial-description-wrapper">
				<h3>Add Product Featured Video For Simple, Variable, Group And External/Affiliate product</h3>
				<div class="tutorial-contents">
					Presting product details in video presentations is priceless. Video can express a thousand words in few seconds. Now, it’s possible to add product featured video for Simple, Variable, Group, And External/Affiliate products. So, get ready to insert self-hosted, YouTube, and Vimeo videos.
				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/add-self-hosted-video-as-feature-product-video/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-youtube-video-in-the-product-variation-gallery" target="_blank" class="button button-docs">Documentation</a>
					<?php if ( ! woo_variation_gallery()->is_pro() ): ?>
						<a href="<?php echo esc_url( woo_variation_gallery()->get_backend()->get_pro_link() ) ?>" target="_blank" class="button button-pro">Upgrade to pro</a>
					<?php endif; ?>
				</div>
			</div>
			<div class="tutorial-image-wrapper">
				<?php if ( ! woo_variation_gallery()->is_pro() ): ?>
					<div class="ribbon"><span><?php esc_html_e( 'PRO', 'woo-variation-gallery' ) ?></span></div>
				<?php endif; ?>
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-4.png' ) ?>">
			</div>
		</li>

		<li>
			<div class="tutorial-image-wrapper">
				<?php if ( ! woo_variation_gallery()->is_pro() ): ?>
					<div class="ribbon"><span><?php esc_html_e( 'PRO', 'woo-variation-gallery' ) ?></span></div>
				<?php endif; ?>
				<img alt="" src="<?php echo woo_variation_gallery()->org_assets_url( '/tutorial-5.png' ) ?>">
			</div>
			<div class="tutorial-description-wrapper">
				<h3>Set Thumbnail Videos in Product Gallery For Simple, Variable, Group And External/Affiliate product</h3>
				<div class="tutorial-contents">
					Besides the product featured video, it’s essential to add a video in the product gallery to describe the product details perfectly. The configuration handles the product video gallery accept self-hosted, YouTube, and Vimeo videos.

				</div>
				<div class="tutorial-buttons">
					<a href="https://demo.getwooplugins.com/woocommerce-variation-gallery/self-hosted-video-thumbnail/" target="_blank" class="button button-live-demo">Live Demo</a>
					<a href="https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-youtube-video-in-the-product-variation-gallery" target="_blank" class="button button-docs">Documentation</a>
					<?php if ( ! woo_variation_gallery()->is_pro() ): ?>
						<a href="<?php echo esc_url( woo_variation_gallery()->get_backend()->get_pro_link() ) ?>" target="_blank" class="button button-pro">Upgrade to pro</a>
					<?php endif; ?>
				</div>
			</div>
		</li>
	</ul>
</div>