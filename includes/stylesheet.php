<?php
defined( 'ABSPATH' ) or die( 'Keep Quit' );
/**
 * @var $gallery_thumbnails_columns
 * @var $gallery_medium_device_thumbnails_columns
 * @var $gallery_small_device_thumbnails_columns
 * @var $gallery_extra_small_device_thumbnails_columns
 * @var $gallery_main_images_to_show
 * @var $gallery_medium_device_main_images_to_show
 * @var $gallery_small_device_main_images_to_show
 * @var $gallery_extra_small_device_main_images_to_show
 * @var $gallery_thumbnails_gap
 * @var $single_image_width
 * @var $gallery_width
 * @var $gallery_margin
 * @var $gallery_medium_device_width
 * @var $gallery_small_device_width
 * @var $gallery_small_device_clear_float
 * @var $gallery_extra_small_device_width
 * @var $gallery_extra_small_device_clear_float
 */
?>
<style type="text/css">
	:root {
		--wvg-thumbnail-item: <?php echo $gallery_thumbnails_columns ?>;
		--wvg-thumbnail-item-gap: <?php echo $gallery_thumbnails_gap ?>px;
		--wvg-main-images-to-show: <?php echo $gallery_main_images_to_show ?>;
		--wvg-single-image-size: <?php echo $single_image_width ?>px;
		--wvg-gallery-width: <?php echo $gallery_width ?>%;
		--wvg-gallery-margin: <?php echo $gallery_margin ?>px;
	}

	/* Default Width */
	.woo-variation-product-gallery {
		max-width: <?php echo $gallery_width ?>% !important;
		width: 100%;
	}

	/* Medium Devices, Desktops */
	@media only screen and (max-width: 992px) {
		:root {
			--wvg-thumbnail-item: <?php echo $gallery_medium_device_thumbnails_columns ?>;
			--wvg-main-images-to-show: <?php echo $gallery_medium_device_main_images_to_show ?>;
		}
		<?php if( $gallery_medium_device_width > 0 ): ?>
		.woo-variation-product-gallery {
			width: <?php echo $gallery_medium_device_width ?>px;
			max-width: 100% !important;
		}
		<?php endif; ?>
	}

	/* Small Devices, Tablets */
	@media only screen and (max-width: 768px) {
		:root {
			--wvg-thumbnail-item: <?php echo $gallery_small_device_thumbnails_columns ?>;
			--wvg-main-images-to-show: <?php echo $gallery_small_device_main_images_to_show ?>;
		}
		<?php if( $gallery_small_device_width > 0 ): ?>
		.woo-variation-product-gallery {
			width: <?php echo $gallery_small_device_width ?>px;
			max-width: 100% !important;
		<?php if( $gallery_small_device_clear_float ): ?> float: none;
		<?php endif; ?>
		}
		<?php endif; ?>
	}

	/* Extra Small Devices, Phones */
	@media only screen and (max-width: 480px) {
		:root {
			--wvg-thumbnail-item: <?php echo $gallery_extra_small_device_thumbnails_columns ?>;
			--wvg-main-images-to-show: <?php echo $gallery_extra_small_device_main_images_to_show ?>;
		}
		<?php if( $gallery_extra_small_device_width > 0 ): ?>
		.woo-variation-product-gallery {
			width: <?php echo $gallery_extra_small_device_width ?>px;
			max-width: 100% !important;
		<?php if( $gallery_extra_small_device_clear_float ): ?> float: none;
		<?php endif; ?>
		}
		<?php endif; ?>
	}

	/* CSS for multiple main images layout - only when multiple images are configured */
	<?php if( $gallery_main_images_to_show > 1 ): ?>
	.woo-variation-gallery-slider .slick-slide {
		margin: 0 3px;
	}

	.woo-variation-gallery-slider.slick-initialized {
		margin: 0 -3px;
	}

	/* Adjust spacing when showing multiple main images */
	.woo-variation-gallery-slider .slick-list {
		margin: 0 -3px;
	}

	/* Override the default CSS that hides images when Slick is initialized with multiple images */
	.woo-variation-gallery-wrapper .woo-variation-gallery-slider.slick-initialized > .wvg-gallery-image {
		display: block !important;
	}
	<?php endif; ?>

	/* Reduce transition time when multiple main images are configured to minimize zoom effect */
	<?php if( $gallery_main_images_to_show > 1 ): ?>
	.woo-variation-gallery-wrapper .woo-variation-gallery-container {
		transition: opacity 0.1s ease-in-out !important;
	}
	<?php endif; ?>
</style>
