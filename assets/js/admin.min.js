!function(){var o={307:function(o,e,a){"use strict";function t(o,e){for(var a=0;a<e.length;a++){var t=e[a];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(o,t.key,t)}}a.r(e),a.d(e,{WooVariationGalleryAdmin:function(){return i}});var r,i=(r=jQuery,function(){function o(){!function(o,e){if(!(o instanceof e))throw new TypeError("Cannot call a class as a function")}(this,o)}var e,a,i;return e=o,i=[{key:"GWPAdmin",value:function(){r().gwp_deactivate_popup&&r().gwp_deactivate_popup("woo-variation-gallery")}},{key:"HandleDiv",value:function(){r(document.body).on("click",".woo-variation-gallery-wrapper .handle-div",(function(){r(this).closest(".woo-variation-gallery-postbox").toggleClass("closed");var o=!r(this).closest(".woo-variation-gallery-postbox").hasClass("closed");r(this).attr("aria-expanded",o)}))}},{key:"ImageUploader",value:function(){r(document).off("click",".add-woo-variation-gallery-image"),r(document).off("click",".remove-woo-variation-gallery-image"),r(document).on("click",".add-woo-variation-gallery-image",this.AddImage),r(document).on("click",".remove-woo-variation-gallery-image",this.RemoveImage),r(".woocommerce_variation").each((function(){var o=r(this).find(".options:first");r(this).find(".woo-variation-gallery-wrapper").insertBefore(o)})),r(document).trigger("woo_variation_gallery_admin_image_uploader_attached",this)}},{key:"AddImage",value:function(e){var a,t=this;e.preventDefault(),e.stopPropagation();var i=r(this).data("product_variation_id"),n=r(this).data("product_variation_loop");if("undefined"!=typeof wp&&wp.media&&wp.media.editor){if(a)return void a.open();(a=wp.media({title:woo_variation_gallery_admin.choose_image,button:{text:woo_variation_gallery_admin.add_image},library:{type:["image"]}})).on("select",(function(){var e=a.state().get("selection").toJSON().map((function(o){if("image"===o.type){var e,a=o.id,t=o.sizes,r=(t=void 0===t?{}:t).thumbnail,l=o.url,c=null!==(e=null==r?void 0:r.url)&&void 0!==e?e:l;return wp.template("woo-variation-gallery-image")({id:a,url:c,product_variation_id:i,loop:n})}})).join("");r(t).parent().prev().find(".woo-variation-gallery-images").append(e),o.Sortable(),o.VariationChanged(t),_.delay((function(){o.ProNotice(t)}),5)})),a.open()}}},{key:"VariationChanged",value:function(o){r(o).closest(".woocommerce_variation").addClass("variation-needs-update"),r("button.cancel-variation-changes, button.save-variation-changes").removeAttr("disabled"),r("#variable_product_options").trigger("woocommerce_variations_input_changed"),r(o).closest(".dokan-product-variation-itmes").addClass("variation-needs-update"),r(".dokan-product-variation-wrapper").trigger("dokan_variations_input_changed"),r(document).trigger("woo_variation_gallery_admin_variation_changed",this)}},{key:"ProNotice",value:function(o){r(o).closest(".woo-variation-gallery-wrapper").find(".woo-variation-gallery-images > li").length,r(o).closest(".woo-variation-gallery-wrapper").find(".woo-variation-gallery-images > li").each((function(e,a){e>=2?(r(a).remove(),r(o).closest(".woo-variation-gallery-wrapper").find(".woo-variation-gallery-pro-button").show()):r(o).closest(".woo-variation-gallery-wrapper").find(".woo-variation-gallery-pro-button").hide()}))}},{key:"RemoveImage",value:function(e){var a=this;e.preventDefault(),e.stopPropagation(),o.VariationChanged(this),_.delay((function(){o.ProNotice(a),r(a).parent().remove()}),1)}},{key:"Sortable",value:function(){r(".woo-variation-gallery-images").sortable({items:"li.image",cursor:"move",scrollSensitivity:40,forcePlaceholderSize:!0,forceHelperSize:!1,helper:"clone",opacity:.65,placeholder:"woo-variation-gallery-sortable-placeholder",start:function(o,e){e.item.css("background-color","#F6F6F6")},stop:function(o,e){e.item.removeAttr("style")},update:function(){o.VariationChanged(this)}})}}],(a=null)&&t(e.prototype,a),i&&t(e,i),Object.defineProperty(e,"prototype",{writable:!1}),o}())}},e={};function a(t){var r=e[t];if(void 0!==r)return r.exports;var i=e[t]={exports:{}};return o[t](i,i.exports,a),i.exports}a.d=function(o,e){for(var t in e)a.o(e,t)&&!a.o(o,t)&&Object.defineProperty(o,t,{enumerable:!0,get:e[t]})},a.o=function(o,e){return Object.prototype.hasOwnProperty.call(o,e)},a.r=function(o){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(o,"__esModule",{value:!0})},function(){function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},o(e)}function e(o){if("function"!=typeof WeakMap)return null;var a=new WeakMap,t=new WeakMap;return(e=function(o){return o?t:a})(o)}jQuery((function(t){Promise.resolve().then((function(){return function(a,t){if(!t&&a&&a.__esModule)return a;if(null===a||"object"!==o(a)&&"function"!=typeof a)return{default:a};var r=e(t);if(r&&r.has(a))return r.get(a);var i={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in a)if("default"!==l&&Object.prototype.hasOwnProperty.call(a,l)){var c=n?Object.getOwnPropertyDescriptor(a,l):null;c&&(c.get||c.set)?Object.defineProperty(i,l,c):i[l]=a[l]}return i.default=a,r&&r.set(a,i),i}(a(307))})).then((function(o){var e=o.WooVariationGalleryAdmin;e.HandleDiv(),e.ImageUploader(),t("#woocommerce-product-data").on("woocommerce_variations_loaded",(function(){e.ImageUploader(),e.Sortable()})),t("#variable_product_options").on("woocommerce_variations_added",(function(){e.ImageUploader(),e.Sortable()})),t(".dokan-product-variation-wrapper").on("dokan_variations_loaded dokan_variations_added",(function(){e.ImageUploader(),e.Sortable()})),t(document).trigger("woo_variation_gallery_admin_loaded")}))}))}()}();