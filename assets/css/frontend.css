/*!
 * Variation Gallery for WooCommerce
 *
 * Author: <PERSON><PERSON> ( <EMAIL> )
 * Date: 3/5/2025, 2:39:49 PM
 * Released under the GPLv3 license.
 */
:root {
  --wvg-thumbnail-item: 1n;
  --wvg-thumbnail-item-gap: 0;
  --wvg-gallery-margin: 0;
}

.woo-variation-gallery-theme-mesmerize * {
  min-width: 0;
  min-height: 0;
}

.woo-variation-product-gallery {
  float: left;
  display: inline-block;
  position: relative;
  margin-bottom: var(--wvg-gallery-margin);
}
.woo-variation-product-gallery .slick-vertical .slick-slide {
  border: 0;
}
.woo-variation-product-gallery.woo-variation-gallery-no-product-thumbnail .woo-variation-gallery-slider-wrapper {
  width: 100% !important;
}
.woo-variation-product-gallery.woo-variation-gallery-no-product-thumbnail .woo-variation-gallery-thumbnail-wrapper {
  display: none;
}

.rtl .woo-variation-product-gallery {
  float: right;
}

.woo-variation-gallery-wrapper {
  position: relative;
  z-index: 0;
  display: block;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-container {
  opacity: 1;
  visibility: visible;
  -webkit-transition: opacity 0.25s ease-in-out;
  -o-transition: opacity 0.25s ease-in-out;
  transition: opacity 0.25s ease-in-out;
  -webkit-filter: none;
          filter: none;
}
.woo-variation-gallery-wrapper.loading-gallery::after {
  content: "";
  position: absolute;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet" x="1124" fill="gray"><g><path d="M10.998 22a.846.846 0 010-1.692 9.308 9.308 0 000-18.616 9.286 9.286 0 00-7.205 3.416.846.846 0 11-1.31-1.072A10.978 10.978 0 0110.998 0c6.075 0 11 4.925 11 11s-4.925 11-11 11z"/><animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0 11 11" to="360 11 11" dur=".8s" calcMode="linear" repeatCount="indefinite"/></g></svg>');
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 50px 50px;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.woo-variation-gallery-wrapper.loading-gallery .woo-variation-gallery-container {
  opacity: 0;
  visibility: hidden;
}
.woo-variation-gallery-wrapper.loading-gallery .woo-variation-gallery-container.preload-style-blur {
  opacity: 0.4;
  visibility: visible;
  -webkit-filter: blur(5px);
          filter: blur(5px);
}
.woo-variation-gallery-wrapper.loading-gallery .woo-variation-gallery-container.preload-style-gray {
  opacity: 0.3;
  visibility: visible;
  -webkit-filter: grayscale(1);
          filter: grayscale(1);
}
.woo-variation-gallery-wrapper .wvg-gallery-thumbnail-image {
  cursor: pointer;
  opacity: 0.3;
  margin: 0;
}
.woo-variation-gallery-wrapper .wvg-gallery-thumbnail-image.current-thumbnail, .woo-variation-gallery-wrapper .wvg-gallery-thumbnail-image:hover {
  opacity: 1;
}
.woo-variation-gallery-wrapper .wvg-gallery-thumbnail-image img {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  height: auto;
  vertical-align: middle;
  margin: 0 !important;
  border-radius: 0;
}
.woo-variation-gallery-wrapper .wvg-gallery-thumbnail-image.wvg-gallery-video-thumbnail div {
  position: relative;
  /*display: block;*/
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  /* flex-direction: row; */
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: flex;
  /* flex-direction: row; */
  justify-content: center;
  align-items: center;
}
.woo-variation-gallery-wrapper .wvg-gallery-thumbnail-image.wvg-gallery-video-thumbnail div::after {
  /*content: "\F236";*/
  content: "\f148";
  -webkit-transform: scaleX(-1);
      -ms-transform: scaleX(-1);
          transform: scaleX(-1);
  position: absolute;
  font-family: dashicons;
  background-color: transparent;
  font-size: 30px;
  color: #ffffff;
  margin: 0;
  padding: 0;
  line-height: 1;
}
.woo-variation-gallery-wrapper .wvg-gallery-thumbnail-image.wvg-gallery-video-thumbnail div::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: black;
  opacity: 0.2;
  margin: 0;
  padding: 0;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-slider {
  position: relative;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-slider::before, .woo-variation-gallery-wrapper .woo-variation-gallery-slider::after {
  content: " ";
  display: table;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-slider::after {
  clear: both;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-slider > .wvg-gallery-image:not(:first-child) {
  display: none;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-slider img {
  display: inline-block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: auto;
  width: 100%;
  max-width: 100%;
  vertical-align: middle;
  margin: 0 !important;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-thumbnail-slider {
  position: relative;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-thumbnail-slider::before, .woo-variation-gallery-wrapper .woo-variation-gallery-thumbnail-slider::after {
  content: " ";
  display: table;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-thumbnail-slider::after {
  clear: both;
}
.woo-variation-gallery-wrapper .wvg-gallery-image {
  text-align: center;
}
.woo-variation-gallery-wrapper .slick-current .wvg-gallery-image {
  display: block !important;
  position: relative;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-slider-wrapper {
  position: relative;
  padding: 0;
  margin: 0;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-thumbnail-wrapper {
  position: relative;
  padding: 0;
  margin: 0;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-thumbnail-wrapper::before, .woo-variation-gallery-wrapper .woo-variation-gallery-thumbnail-wrapper::after {
  content: " ";
  display: table;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-thumbnail-wrapper::after {
  clear: both;
}
.woo-variation-gallery-wrapper .woo-variation-gallery-trigger {
  position: absolute;
  top: 0.5em;
  right: 0.5em;
  font-size: 2em;
  z-index: 999;
  width: 36px;
  height: 36px;
  background: #FFFFFF;
  border-radius: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-decoration: none;
  color: #000000;
}

.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 2);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 3);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 4);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 5);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 6);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 7);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 8);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
  /*display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;*/
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
  margin-bottom: var(--wvg-thumbnail-item-gap);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
  margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
  display: block !important;
  /*display: flex !important;
  justify-content: center;
  align-items: center;*/
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
  margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
  overflow: hidden;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
  opacity: 1;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
  width: 80%;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
  width: 20%;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
  width: 100%;
  height: 30px;
  /*left: 0;
  right: 0;*/
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
  top: 0;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
  top: auto;
  bottom: 0;
}
.woo-variation-gallery-thumbnail-position-left-left .woo-variation-gallery-thumbnail-slider {
  margin-right: var(--wvg-thumbnail-item-gap);
}

@media only screen and (min-width: 767px) {
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;*/
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-bottom: var(--wvg-thumbnail-item-gap);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 80%;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 20%;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 100%;
    height: 30px;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    top: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    top: auto;
    bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-thumbnail-slider {
    margin-right: var(--wvg-thumbnail-item-gap);
  }
}
@media only screen and (max-width: 767px) {
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;*/
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-bottom: var(--wvg-thumbnail-item-gap);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 80%;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 20%;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 100%;
    height: 30px;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    top: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    /*top: calc(100% - 30px + 1 * var(--wvg-thumbnail-item-gap) / 2);*/
    top: auto;
    bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-right .woo-variation-gallery-thumbnail-slider {
    margin-left: var(--wvg-thumbnail-item-gap);
  }
}

@media only screen and (min-width: 767px) {
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;*/
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-bottom: var(--wvg-thumbnail-item-gap);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 80%;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 20%;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 100%;
    height: 30px;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    top: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    top: auto;
    bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-thumbnail-slider {
    margin-right: var(--wvg-thumbnail-item-gap);
  }
}
@media only screen and (max-width: 767px) {
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;*/
    display: block;
    clear: both;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-right: var(--wvg-thumbnail-item-gap);
    display: inline-block;
    float: left;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-left: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-right: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: 0 calc(var(--wvg-thumbnail-item-gap) / 2);
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 100%;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 100%;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 30px;
    height: 100%;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    left: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    left: auto;
    right: 0;
  }
  .woo-variation-gallery-thumbnail-position-left-bottom .woo-variation-gallery-thumbnail-slider {
    margin-top: var(--wvg-thumbnail-item-gap);
  }
}

@media only screen and (min-width: 767px) {
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;*/
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-bottom: var(--wvg-thumbnail-item-gap);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 80%;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 20%;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 100%;
    height: 30px;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    top: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    /*top: calc(100% - 30px + 1 * var(--wvg-thumbnail-item-gap) / 2);*/
    top: auto;
    bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-thumbnail-slider {
    margin-left: var(--wvg-thumbnail-item-gap);
  }
}
@media only screen and (max-width: 767px) {
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;*/
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-bottom: var(--wvg-thumbnail-item-gap);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 80%;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 20%;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 100%;
    height: 30px;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    top: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    top: auto;
    bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-left .woo-variation-gallery-thumbnail-slider {
    margin-right: var(--wvg-thumbnail-item-gap);
  }
}

.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 2);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 3);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 4);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 5);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 6);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 7);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  height: calc(var(--thumb-wrapper-margin-remove) / 8);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
  margin-bottom: 0;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
  /*display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;*/
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
  margin-bottom: var(--wvg-thumbnail-item-gap);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
  margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
  display: block !important;
  /*display: flex !important;
  justify-content: center;
  align-items: center;*/
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
  margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
  overflow: hidden;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
  opacity: 1;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
  width: 80%;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
  width: 20%;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
  width: 100%;
  height: 30px;
  /*left: 0;
  right: 0;*/
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
  -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
          transform: rotate(90deg);
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
  top: 0;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
  /*top: calc(100% - 30px + 1 * var(--wvg-thumbnail-item-gap) / 2);*/
  top: auto;
  bottom: 0;
}
.woo-variation-gallery-thumbnail-position-right-right .woo-variation-gallery-thumbnail-slider {
  margin-left: var(--wvg-thumbnail-item-gap);
}

@media only screen and (min-width: 767px) {
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;*/
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-bottom: var(--wvg-thumbnail-item-gap);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 80%;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 20%;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 100%;
    height: 30px;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    top: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    /*top: calc(100% - 30px + 1 * var(--wvg-thumbnail-item-gap) / 2);*/
    top: auto;
    bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-thumbnail-slider {
    margin-left: var(--wvg-thumbnail-item-gap);
  }
}
@media only screen and (max-width: 767px) {
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;*/
    display: block;
    clear: both;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-right: var(--wvg-thumbnail-item-gap);
    display: inline-block;
    float: left;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-left: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-right: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: 0 calc(var(--wvg-thumbnail-item-gap) / 2);
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 100%;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 100%;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 30px;
    height: 100%;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    left: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    left: auto;
    right: 0;
  }
  .woo-variation-gallery-thumbnail-position-right-bottom .woo-variation-gallery-thumbnail-slider {
    margin-top: var(--wvg-thumbnail-item-gap);
  }
}

@media only screen and (min-width: 767px) {
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;*/
    display: block;
    clear: both;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-right: var(--wvg-thumbnail-item-gap);
    display: inline-block;
    float: left;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-left: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-right: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: 0 calc(var(--wvg-thumbnail-item-gap) / 2);
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 100%;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 100%;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 30px;
    height: 100%;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    left: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    left: auto;
    right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-thumbnail-slider {
    margin-top: var(--wvg-thumbnail-item-gap);
  }
}
@media only screen and (max-width: 767px) {
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-bottom: var(--wvg-thumbnail-item-gap);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 80%;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 20%;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 100%;
    height: 30px;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    top: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    top: auto;
    bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-left .woo-variation-gallery-thumbnail-slider {
    margin-right: var(--wvg-thumbnail-item-gap);
  }
}

@media only screen and (min-width: 767px) {
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    width: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;*/
    display: block;
    clear: both;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-right: var(--wvg-thumbnail-item-gap);
    display: inline-block;
    float: left;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-left: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-right: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: 0 calc(var(--wvg-thumbnail-item-gap) / 2);
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 100%;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 100%;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 30px;
    height: 100%;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    left: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    left: auto;
    right: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-thumbnail-slider {
    margin-top: var(--wvg-thumbnail-item-gap);
  }
}
@media only screen and (max-width: 767px) {
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 2);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 3);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 4);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 5);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 6);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 7);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
    --thumb-wrapper-width: 100%;
    --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
    --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
    height: calc(var(--thumb-wrapper-margin-remove) / 8);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
    margin-bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
    display: none;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
    /*display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
    margin-bottom: var(--wvg-thumbnail-item-gap);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
    margin-top: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
    margin-bottom: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
    display: block !important;
    /*display: flex !important;
    justify-content: center;
    align-items: center;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
    margin: calc(var(--wvg-thumbnail-item-gap) / 2) 0;
    overflow: hidden;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
    opacity: 1;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
    width: 80%;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
    width: 20%;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    width: 100%;
    height: 30px;
    /*left: 0;
    right: 0;*/
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
    top: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
    /*top: calc(100% - 30px + 1 * var(--wvg-thumbnail-item-gap) / 2);*/
    top: auto;
    bottom: 0;
  }
  .woo-variation-gallery-thumbnail-position-bottom-right .woo-variation-gallery-thumbnail-slider {
    margin-left: var(--wvg-thumbnail-item-gap);
  }
}

.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 1);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  width: calc(var(--thumb-wrapper-margin-remove) / 2);
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(2n) {
  margin-right: 0;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 2);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  width: calc(var(--thumb-wrapper-margin-remove) / 3);
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(3n) {
  margin-right: 0;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 3);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  width: calc(var(--thumb-wrapper-margin-remove) / 4);
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(4n) {
  margin-right: 0;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 4);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  width: calc(var(--thumb-wrapper-margin-remove) / 5);
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(5n) {
  margin-right: 0;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 5);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  width: calc(var(--thumb-wrapper-margin-remove) / 6);
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(6n) {
  margin-right: 0;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 6);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  width: calc(var(--thumb-wrapper-margin-remove) / 7);
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(7n) {
  margin-right: 0;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div {
  --thumb-wrapper-width: 100%;
  --total-margin: calc(var(--wvg-thumbnail-item-gap) * 7);
  --thumb-wrapper-margin-remove: calc(var(--thumb-wrapper-width) - var(--total-margin));
  width: calc(var(--thumb-wrapper-margin-remove) / 8);
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(8n) {
  margin-right: 0;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-2:not(.slick-initialized) > div:nth-child(n+3) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-3:not(.slick-initialized) > div:nth-child(n+4) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-4:not(.slick-initialized) > div:nth-child(n+5) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-5:not(.slick-initialized) > div:nth-child(n+6) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-6:not(.slick-initialized) > div:nth-child(n+7) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-7:not(.slick-initialized) > div:nth-child(n+8) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-columns-8:not(.slick-initialized) > div:nth-child(n+9) {
  display: none;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) {
  /*display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;*/
  display: block;
  clear: both;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider:not(.slick-initialized) > div {
  margin-right: var(--wvg-thumbnail-item-gap);
  display: inline-block;
  float: left;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized {
  margin-left: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
  margin-right: calc(-1 * var(--wvg-thumbnail-item-gap) / 2);
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .wvg-gallery-thumbnail-image {
  display: block !important;
  /*display: flex !important;
  justify-content: center;
  align-items: center;*/
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide > div {
  margin: 0 calc(var(--wvg-thumbnail-item-gap) / 2);
  overflow: hidden;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-current .wvg-gallery-thumbnail-image, .woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center.wvg-gallery-thumbnail-image,
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-slider.slick-initialized .slick-slide.slick-center .wvg-gallery-thumbnail-image {
  opacity: 1;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-slider-wrapper {
  width: 100%;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .woo-variation-gallery-thumbnail-wrapper {
  width: 100%;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
  width: 30px;
  height: 100%;
  /*left: 0;
  right: 0;*/
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow::before {
  -webkit-transform: rotate(0deg);
      -ms-transform: rotate(0deg);
          transform: rotate(0deg);
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .wvg-thumbnail-prev-arrow {
  left: 0;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-container .wvg-thumbnail-next-arrow {
  left: auto;
  right: 0;
}
.woo-variation-gallery-thumbnail-position-bottom-bottom .woo-variation-gallery-thumbnail-slider {
  margin-top: var(--wvg-thumbnail-item-gap);
}

.woo-variation-gallery-trigger-position-top-right {
  right: 0.5em;
}
.woo-variation-gallery-trigger-position-top-left {
  right: auto;
  left: 0.5em;
}
.woo-variation-gallery-trigger-position-bottom-right {
  top: auto !important;
  bottom: 0.5em;
}
.woo-variation-gallery-trigger-position-bottom-left {
  top: auto !important;
  right: auto;
  bottom: 0.5em;
  left: 0.5em;
}

.rtl .woo-variation-gallery-trigger-position-top-right {
  right: auto;
  left: 0.5em;
}
.rtl .woo-variation-gallery-trigger-position-top-left {
  right: 0.5em;
}
.rtl .woo-variation-gallery-trigger-position-bottom-right {
  top: auto !important;
  bottom: 0.5em;
}
.rtl .woo-variation-gallery-trigger-position-bottom-left {
  top: auto !important;
  right: auto;
  bottom: 0.5em;
  left: 0.5em;
}

.woo-variation-gallery-thumbnail-wrapper {
  overflow: hidden;
}

.woo-variation-gallery-slider-wrapper {
  overflow: hidden;
}
.woo-variation-gallery-slider-wrapper .wvg-slider-prev-arrow,
.woo-variation-gallery-slider-wrapper .wvg-slider-next-arrow {
  position: absolute;
  top: 50%;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
  cursor: pointer;
  font-size: 16px;
  margin-top: -20px;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  opacity: 0.5;
}
.woo-variation-gallery-slider-wrapper .wvg-slider-prev-arrow::before,
.woo-variation-gallery-slider-wrapper .wvg-slider-next-arrow::before {
  color: #FFFFFF;
}
.woo-variation-gallery-slider-wrapper .wvg-slider-prev-arrow:hover,
.woo-variation-gallery-slider-wrapper .wvg-slider-next-arrow:hover {
  background: rgba(0, 0, 0, 0.9);
  opacity: 1;
}
.woo-variation-gallery-slider-wrapper .wvg-slider-prev-arrow {
  left: -40px;
}
.woo-variation-gallery-slider-wrapper .wvg-slider-next-arrow {
  right: -40px;
}
.woo-variation-gallery-slider-wrapper:hover .wvg-slider-prev-arrow {
  left: 0;
}
.woo-variation-gallery-slider-wrapper:hover .wvg-slider-next-arrow {
  right: 0;
}

.woo-variation-gallery-thumbnail-slider {
  -webkit-transition: height 100ms ease;
  -o-transition: height 100ms ease;
  transition: height 100ms ease;
  /*.wvg-thumbnail-prev-arrow {
  	left: calc(var(--wvg-thumbnail-item-gap) / 2);
  }

  .wvg-thumbnail-next-arrow {
  	right: calc(var(--wvg-thumbnail-item-gap) / 2);
  }*/
}
.woo-variation-gallery-thumbnail-slider .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-slider .wvg-thumbnail-next-arrow {
  position: absolute;
  top: 0;
  width: 30px;
  height: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
  cursor: pointer;
  font-size: 14px;
  -webkit-transition: opacity 0.3s;
  -o-transition: opacity 0.3s;
  transition: opacity 0.3s;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  visibility: hidden;
  opacity: 0;
}
.woo-variation-gallery-thumbnail-slider .wvg-thumbnail-prev-arrow::before,
.woo-variation-gallery-thumbnail-slider .wvg-thumbnail-next-arrow::before {
  color: #FFFFFF;
}
.woo-variation-gallery-thumbnail-slider .wvg-thumbnail-prev-arrow:hover,
.woo-variation-gallery-thumbnail-slider .wvg-thumbnail-next-arrow:hover {
  background: rgba(0, 0, 0, 0.9);
}
.woo-variation-gallery-thumbnail-slider:hover .wvg-thumbnail-prev-arrow,
.woo-variation-gallery-thumbnail-slider:hover .wvg-thumbnail-next-arrow {
  opacity: 1;
  visibility: visible;
}

.wvg-single-gallery-iframe-container,
.wvg-single-gallery-video-container {
  position: relative;
  overflow: hidden;
  display: block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  background-color: #000000;
  --_video_ratio: 1 / 1;
  z-index: 0;
}
.wvg-single-gallery-iframe-container iframe, .wvg-single-gallery-iframe-container object, .wvg-single-gallery-iframe-container embed, .wvg-single-gallery-iframe-container video,
.wvg-single-gallery-video-container iframe,
.wvg-single-gallery-video-container object,
.wvg-single-gallery-video-container embed,
.wvg-single-gallery-video-container video {
  position: relative;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  display: block;
  aspect-ratio: var(--_video_ratio);
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: left top;
     object-position: left top;
}

.pswp {
  z-index: 9999;
}

.pswp__button {
  z-index: 999;
}

.pswp .pswp__button--arrow--left:before,
.pswp .pswp__button--arrow--right:before {
  background-color: #000000 !important;
}
