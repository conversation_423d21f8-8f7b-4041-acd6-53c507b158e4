=== Additional Variation Images Gallery for WooCommerce ===
Contributors: EmranAhmed, getwooplugins
Tags: woocommerce variation image gallery, additional variation image gallery, product variation image gallery, product variation image, variation images gallery, additional image, additional variation image, WooCommerce variation product thumbnail gallery
Requires PHP: 7.4
Stable tag: 1.3.28
Requires at least: 5.7
Tested up to: 6.7
WC requires at least: 5.8
WC tested up to: 9.7
License: GPLv3
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Allows inserting multiple images per variation to let your store customers to see different sets of images when WooCommerce product variations are switched.

== Description ==

=== How To Install Additional Variation Images for WooCommerce Plugin ===

[youtube https://www.youtube.com/watch?v=BoLzP2KGh_k]

Do you want multiple images for WooCommerce product attribute variation to drive sales and conversion? The Additional Variation Images Gallery for WooCommerce is an incredible option to pick.

WooCommerce offers an option to insert only one image per product variation. It means there is no option to add more than a single variation image when it is essential to show additional product images to boost sales.

To unlock this limitation, the WooCommerce Additional Variation Images Gallery plugin. <strong>It allows to upload multiple images for each product variation. </strong>

So, with the help of this <strong>additional variation images plugin for WooCommerce</strong>, it's possible to show different sets of images to visitors when they switch product variations like Color, Style, and size simultaneously. 

If you are looking for a plugin to insert multiple images per variation for WooCommerce variable product, you are in the right place. The plugin is well-documented and optimized to support major WooCommerce themes in the market.

## Key Feature Comes with WooCommerce Additional Variation Images Gallery Plugin ##

👉 **Display multiple images per product variation.**

By default, WooCommerce allows inserting only one photo per product variation. This variation images gallery plugin will enable you to insert various pictures in variable product variations. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-images-in-the-variation-gallery)
<hr />

👉 **Adjust Variation Gallery Width For Desktop, Tablet, Mobile**

It offers options to adjust the variation images gallery width for Desktop, Tablet, and Mobile Phones to tailor your variation gallery to all available viewing devices. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#variation-video-width-adjustment)
<hr />

👉 **Move Gallery Thumbnails to Left, Right, and Bottom**

WooCommerce initially shows the gallery thumbnail in the bottom position. By installing the WooCommerce Extra Variation Images Gallery plugin, you can move them to the left and right positions. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#left-right-bottom-gallery-thumbnail-position)
<hr />

👉 **Enable Thumbnail Slider**

WooCommerce shows the product gallery thumbnails in the grid style. It will show one after another. We programmed the WooCommerce Variation Multiple Images Gallery plugin to convert the grid into a thumbnail slider.

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#enable-slider-for-woocommerce-product-images-gallery)
<hr />

👉 **Product Feature Image Auto slider**

WooCommerce different images for variations plugin enables auto slider for product feature images. It means customers don't want to press the arrow to check product photos one by one.

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#product-feature-image-auto-slider)
<hr />

👉 **Define Product Image Slider Speed**

Slider speed helps customers to see the product images quickly. To fulfill this requirement, additional variation images for WooCommerce plugin have the option to increase or decrease the sliding time according to project requirements. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#slider-or-fade-for-thumbnails-gallery)
<hr />

👉 **Slider or Fade Choose what you need**

There are two options combined to present product images smartly. Either you can enable the sliding option or the fading one. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#define-thumbnail-item-number-gaps)
<hr />

👉 **Define Thumbnail Item Number & Gaps**

Generally, you see the four images in the product thumbnail slider initially. You can set the number from 2 to 8. Besides that, you can define the gap between each thumbnail item. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#define-thumbnail-item-number-gaps)
<hr />

👉 **Arrow control for Product Image and Thumbnails Slider**

Product feature images and thumbnail sliders come with arrows to navigate the variation images. It is possible to disable arrows for both sliders.

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#gallery-slider-thumbnail-arrow-control)
<hr />

👉 **Product Featured Image Zoom**

Zoom allows the customers to see the images magnifying the details. It boots product conversion. We keep the option enabled, and you can disable it anytime. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#product-image-zoom-display-position)
<hr />
 
👉 **Popup Icon Display Position For Product Featured Image**
To allow users to see the product featured image closely, it has an option to open the product image in a lightbox. So, an icon is enabled on the feature product image to trigger the lightbox. You can move the icon top left corner, top right corner, bottom left corner and bottom right corner. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#product-image-zoom-display-position)
<hr />

👉 **Variation Image Shorting Option**

WooCommerce Additional Variation Images Gallery plugin comes with a design that saves development time. You can reorder your variation uploaded images according to your need. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#variation-image-sorting)
<hr />

👉 **Preloader Settings For Product Image Gallery**

 Preloader is an excellent option when it comes to deal loading images. With our Multiple Images Variation For WooCommerce plugin, you can select preloader style or disable the preloader altogether. 
 
 [Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#set-gallery-preloader)
<hr />

👉 **Hide Featured Image**

Sometimes, hiding the featured image from the product image gallery is essential. So, we added the option to hide the featured images for the variation images gallery plugin. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#disable-product-gallery-for-selected-product-type)
<hr />

👉 **Disable Gallery For the Selected Product Type.** 

WooCommerce comes with four product types. Simple, Variable, Grouped, and External/Affiliate. Additional Variation Image gallery loads the plugin script for all available product types. You can disable the images gallery plugin script for your desired product type. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#disable-product-gallery-for-selected-product-type)
<hr />

👉 **Supports Unlimited WooCommerce Theme** 

WooCommerce Additional Variation Images plugin has excellent theme compatibility and supports all the major WoCommerce themes in the market.

👉 **Migrate From Other WooCommerce Additional Variation Plugins**

Migrating from other Variation Images Gallery Plugin to ours is easy. We have added an option for quick migration. 

👉 **Automatic Plugin Updates**

The Variation Product Gallery plugin is regularly updated to add new features. We ensure seamless integration with top WordPress plugins and themes. Our developers are ready to fix any issue as soon as they find problems. 

👉 **Exclusive Technical Support**

Don't hesitate to contact our support if you haven't found what you are looking for. We have a team of Support Engineers ready to provide incredible support. You can ask questions in the support forum, open a support ticket, or contact us through our live chat system.

## 🏆 Customers' Feedback For Variation Images Gallery Plugin ##

[meloz](https://wordpress.org/support/topic/great-product-and-awesome-support-8/): 
> "I have been using this plugin for a while now and was working exactly how I wanted it."

>Then an update came through for my theme, and it stopped showing the variation images when changing the selection. I googled for ages and looked everywhere how to fix it.


>I then asked support and Hakik very kindly and quickly investigated the issue and gave me some code to add to my website and it is now working again.

>Thanks team, you rock!'

[Riaan Aggenbag](https://wordpress.org/support/topic/dont-believe-the-haters/): 
> 'Really good support from Tanvirul when I ran into some CSS issues.

>Thanks ya’ll.'

[Elzette Roelofse](https://wordpress.org/support/topic/works-beautifully-136/): 
> 'Perfect combination with their Additional Variation Images for WooCommerce plugin.

> Great support too!'

= PREMIUM FEATURES OF ADDITIONAL VARIATION IMAGES FOR WOOCOMMERCE =

[Live Demo](https://bit.ly/live-demo-gallery-org) | [Upgrade to PRO](http://bit.ly/variation-image-gl-pro) | [Documentation](http://bit.ly/wvariation-image-gallery-doc) | [Support](https://getwooplugins.com/tickets/?utm_source=wordpress.org&utm_medium=README&utm_campaign=woo-variation-gallery)

=== How To Insert Videos on Additional Variation Image Gallery (Pro Feature) ===

[youtube https://www.youtube.com/watch?v=wopbjNkJNEc]

👉  **Add Unlimited Images For Each WooCommerce Product Variation**

The free version of this plugin allows showing two extra photos per product variation. However, the advanced version of the WooCommerce Variable Product Gallery plugin adds an option to insert unlimited images for the product variation images gallery.

  [Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-images-in-the-variation-gallery)
<hr />

👉  **Add Video To WooCommerce Variable Product Gallery** 

With the Variation Image Gallery plugin, you can woocommerce video in product gallery. So, you can easily upload <strong>YouTube</strong>, <strong>Vimeo</strong>, and <strong>self-hosted</strong> videos directly into the variable product gallery. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-youtube-video-in-the-product-variation-gallery)
<hr />
👉  **Add Video To WooCommerce Simple Product Gallery**

Besides the variable product, it's possible to insert <strong>YouTube</strong>, <strong>Vimeo</strong>, and <strong>self-hosted</strong> videos in the WooCommerce simple product images gallery. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#show-video-in-simple-product-gallery-woocommerce-default-gallery)
<hr />

👉  **Add Video To WooCommerce Group Product Gallery**

If you want to show  <strong>YouTube</strong>, <strong>Vimeo</strong>, and <strong>self-hosted</strong> videos in the grouped products, with multiple images gallery plugin would get an easy option. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-videos-in-grouped-product-image-gallery)
<hr />

👉  **Add Video To WooCommerce External/Affiliate Product Gallery** 

External products take users to the sourced website. If you want to engage customers and add a video to the WooCommerce product gallery for external/affiliate products, you are allowed to insert <strong>YouTube</strong>, <strong>Vimeo</strong>, and <strong>self-hosted</strong> videos easily. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#upload-videos-in-external-affiliate-product-image-gallery)
<hr />

👉  **Add WooCommerce Featured Video For Variable Product**

WooCommerce feature video for variable products will be visible to end-users without changing any variation. If you want to show a video in the WooCommerce feature image area, you can show <strong>YouTube</strong>, <strong>Vimeo</strong>, and <strong>self-hosted</strong> with ease. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#add-featured-video-in-variable-product)
<hr />

👉  **Add WooCommerce Featured Video For Simple Product**

If you want a WooCommerce featured video for the simple product type, we added control to  Additional variation image gallery plugin. It will help you to showcase easily product details with <strong>YouTube</strong>, <strong>Vimeo</strong>, and <strong>self-hosted</strong> video. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#add-featured-video-in-simple-product)
<hr />

👉  **Add WooCommerce Product Featured Video For Grouped Product**

The grouped product comes grouped with a couple of products. If you need to show extra details for the grouped product, you can easily show details with <strong>YouTube</strong>, <strong>Vimeo</strong>, and <strong>self-hosted</strong> video. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#add-featured-video-in-grouped-product)
<hr />

👉  **Add WooCommerce Product Featured Video For External/Affiliate Product**

The video expresses more words than images. With variation images gallery plugin, you can present more product insights with <strong>YouTube</strong>, <strong>Vimeo</strong>, and <strong>self-hosted</strong> video. 

[Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-gallery/#add-featured-video-in-external-affiliate-product)
<hr />

## 🏆 Customers' Feedback For Variation Images Gallery Plugin ##

[martamagnetti](https://wordpress.org/support/topic/best-support-ever-132/): 
> 'Great plugin, but most of all 10 stars to the incredible support service.'

[check07](https://wordpress.org/support/users/check07/): 
> 'This plugin just give me the best presentation of my image gallery.
> – Easy to add additional images
> – Wonderful load of galleries
> – Easy integration with my theme
> Thank you guys!

>Great support too!'

[lwhemesath65](https://wordpress.org/support/topic/love-the-stuff/): 
> 'I got the free version and it works perfectly fine. I had a question and support answered right away. That’s awesome. 
>I wish all plugin vendors would have such a high standard.'

= Sites Built With Additional Variation Images for WooCommerce Plugin =
<blockquote>
<ul>
<li><a target="_blank" href="http://j.mp/variation-gallery-demo"> Built With: Flatsome Theme | Niche: Clothing Store.</a> </li>

<li><a target="_blank" href="http://j.mp/variation-gallery-demo"> Built With: Divi Theme | Niche: Automobile Wheels Store.</a> </li>

<li><a target="_blank" href="http://j.mp/variation-gallery-demo"> Built With: Avada Theme | Niche: Motorcycle Helmet Store. </a></li>

<li><a target="_blank" href="http://j.mp/variation-gallery-demo"> Built With: Avada WooCommerce Theme | Niche: Ladies Purse Bag Store.</a> </li>

<li><a target="_blank" href="http://j.mp/variation-gallery-demo"> Built With: Shopkeeper WooCommerce Theme | Niche: Sandal Store. </a></li>

<li><a target="_blank" href="http://j.mp/variation-gallery-demo"> Built With: Aurum WooCommerce Theme | Niche: Mobile Case Store. </a></li>

</ul>
</blockquote>


= Compatible WooCommerce Plugin =

<blockquote>

<ul>
<li><a target="_blank" href="http://bit.ly/wvg-org-item-page">Variation Swatches For WooCommerce</a></li>
</ul>
<ul>
<li><a target="_blank" href="http://bit.ly/swatches-link-to-duplicator-readme">Variation Duplicator For WoooCommerce</a></li>
</ul>
<ul>
<li><a target="_blank" href="https://wordpress.org/plugins/woo-cart-redirect-to-checkout-page">Add to Cart Redirect for WooCommerce</a></li>
</ul>

</blockquote>

= Forum and Feature Request =

<blockquote>
<h4>Pro Version</h4>
<ul>
<li><a target="_blank" href="https://getwooplugins.com/plugins/woocommerce-variation-gallery?utm_source=wordpress.org&utm_medium=README&utm_campaign=woo-variation-gallery">Pro Version</a></li>
</ul>
<ul>
<li><a target="_blank" href="http://bit.ly/wvariation-image-gallery-doc">Documentation</a></li>
</ul>
<h4>For Quick Support, feature request and bug reporting</h4>
<ul>
<li><a target="_blank" href="http://bit.ly/getwoopluginsgroup">Join Our Facebook Group</a></li>
</ul>
<h4>For more information</h4>
<ul>
<li><a target="_blank" href="https://getwooplugins.com/?utm_source=wordpress.org&utm_medium=README&utm_campaign=woo-variation-swatches">Visit Our Official Website</a></li>
</ul>
</blockquote>

= Featured on Popular Blog Sites =

<blockquote>

<ul>
<li><a target="_blank" href="https://wpcred.com">wpcred.com</a></li>
</ul>
<ul>
<li><a target="_blank" href="https://storepress.com">storepress.com</a></li>
</ul>

</blockquote>


== Installation ==

### Automatic Install From WordPress Dashboard

1. Login to your admin panel
2. Navigate to Plugins -> Add New
3. Search **Additional Variation Images Gallery for WooCommerce**
4. Click install and activate respectively.

### Manual Install From WordPress Dashboard

If your server is not connected to the Internet, then you can use this method-

1. Download the plugin by clicking on the red button above. A ZIP file will be downloaded.
2. Login to your site's admin panel and navigate to Plugins -> Add New -> Upload.
3. Click choose file, select the plugin file and click install

### Install Using FTP

If you are unable to use any of the methods due to internet connectivity and file permission issues, then you can use this method-

1. Download the plugin by clicking on the red button above. A ZIP file will be downloaded.
2. Unzip the file.
3. Launch your favorite FTP client. Such as FileZilla, FireFTP, CyberDuck etc. If you are a more advanced user, then you can use SSH too.
4. Upload the folder to `wp-content/plugins/`
5. Log in to your WordPress dashboard.
6. Navigate to Plugins -> Installed
7. Activate the plugin

== Frequently Asked Questions ==

= Is it compatible with any kinds of WooCommerce Theme? =

Yes, it's compatible with any WooCommerce theme including OceanWP / Astra / Flatsome / Avada / Storefront / WR Nitro / Divi / BeTheme / Zerif Lite / Hestia / Shopisle and more. But sometimes it may require small css tweak.

= Does it work on MultiSite? =

Yes, it is.

== Screenshots ==

1. Gallery Output
2. Gallery Zoom Feature
3. Gallery Settings
4. Adding Images on gallery

== Changelog ==

= 1.3.28 - 05-03-2025 =

* Fix: CDN Image Load Issue on admin.
* Update: Update Template version.
* Add: WC 9.7+ compatibility.

= 1.3.27 - 16-02-2025 =

* Fix: Settings table broken issue.
* Add: WC 9.6+ compatibility.

= 1.3.26 - 11-12-2024 =

* Fix: JS Issue on save settings.
* Update: "Hide Main Product Image" settings.
* Add: WP 6.7+ compatibility.
* Add: WC 9.4+ compatibility.

= 1.3.25 - 15-09-2024 =

* Fix: Mobile issue on external videos.
* Add: WooCommerce 9.3+ compatibility.

= 1.3.24 - 25-08-2024 =

* Fix: `get_current_screen` function issue for MultiVendor plugins.
* Add: WooCommerce 9.2+ compatibility.

= 1.3.23 - 01-08-2024 =

* Update: Gallery template file version.
* Fix: Repeated image load.
* Add: WordPress 6.6+ compatibility.
* Add: WooCommerce 9.1+ compatibility.

= 1.3.22 - 17-04-2024 =

* Add: MultiVendorX Plugin Compatible.
* Add: Filter `woo_variation_gallery_zoom_icon_html` to change zoom icon markup
* Add: Option to include default gallery images with variation images.
* Fix: admin screen issue.
* Add: WordPress 6.5+ compatibility.
* Add: WooCommerce 8.7+ compatibility.

= 1.3.21 - 21-11-2023 =

* Add: twentytwentyfour theme support.
* Add: WordPress 6.4+ compatibility.
* Add: WooCommerce 8.3+ compatibility.

= 1.3.20 - 07-09-2023 =

* Add: twentytwentythree theme support.
* Add: WooCommerce 8.0+ compatibility.
* Add: WordPress 6.3+ compatibility.

= 1.3.19 - 18-06-2023 =

* Add: WooCommerce 7.8+ compatibility.
* Update: Additional Rest API Response
* Update: Product image template version

= 1.3.18 - 22-05-2023 =

* Add: WooCommerce 7.7+ compatibility.
* Fix: Safari browser issue during vimeo pause

= 1.3.17 - 13-04-2023 =

* Update: Improve Video Function to support YouTube shorts.
* Add: WP 6.2+ compatibility.
* ADD: Action `woo_variation_product_gallery_slider_start` added to slider markup start
* ADD: Action `woo_variation_product_gallery_slider_end` added to slider markup end

= 1.3.16 - 28-03-2023 =

* Fix: Variation loading issue when no variation image available

= 1.3.15 - 27-03-2023 =

* Fix: PHP issue for PHP 8.* migration

= 1.3.14 - 23-03-2023 =

* Update: WooCommerce 7.5 Compatibility
* Fix: PHP @putenv issue during migration
* Update: Support WooCommerce High-Performance Order Storage
* Fix: WPML Global Image translation issue.

= 1.3.13 - 13-10-2022 =

* Update: WooCommerce 7.1 Compatibility
* Update: Gallery width based on thumbnail availability

= 1.3.12 - 03-10-2022 =

* Update: WordPress 6.1 and WooCommerce 7.0 Compatibility

= 1.3.11 - 28-08-2022 =

* Update: Settings Scripts

= 1.3.10 - 23-08-2022 =

* Fix: Settings Issue

= 1.3.9 - 14-08-2022 =

* Update: WooCommerce Compatibility

= 1.3.8 - 31-07-2022 =

* Update: Dependency Script

= 1.3.7 - 20-07-2022 =

* Update: Settings script

= 1.3.6 - 08-07-2022 =

* Update: Preloader option

= 1.3.5 - 16-05-2022 =

* Fix: Thumbnail RTL Issue

= 1.3.4 - 13-05-2022 =

* Update: Settings script Updated
* Update: Migration Script

= 1.3.3 - 26-05-2022 =

* Update: Dependency script Updated
* Update: WordPress Compatibility

= 1.3.2 - 18-05-2022 =

* Update: Settings Classes
* Update: WooCommerce and WordPress Compatibility

= 1.3.1 - 15-03-2022 =

* Fixed: Product edit panel expand issue
* Fixed: Flatsome theme

= 1.3.0 - 25-02-2022 =

* Update: Plugin structure and settings panel
* Update: Add WordPress 5.9+ and WooCommerce 6.2+ Compatibility

= 1.2.9 =

* Add: Option to remove default featured image from gallery

= 1.2.8 =

* Update: trim imported url before import
* Update: Add WordPress 5.8+ and WooCommerce 5.6+ Compatibility
* Fix: variation duplicator `image_saved_from` issue.

= 1.2.7 =

* Add: Add filter for Export / Import
* Update: Add WordPress 5.8+ and WooCommerce 5.6+ Compatibility

= 1.2.6 =

* Fix: PHP issue

= 1.2.5 =

* Add: Option to adjust gallery thumbnail image width.
* Add: Filter `disable_woo_variation_gallery` to disable gallery on specific page or product(s)
* Add: Option to disable gallery on specific product type
* Update: Add WordPress 5.7+ and WooCommerce 5.2 Compatibility
* Fix: Mesmerize CSS Issue

= 1.2.4 =

* Update: WordPress 5.7 and WooCommerce 5.1 Compatibility

= 1.2.3 =

* Add: Yith Banner plugin support
* Add: Dokan Multivendor plugin support
* Update: Change wrapper class to `woo_variation_gallery_product_wrapper_classes`
* Add: Action added `woo_variation_product_gallery_start`
* Add: Action added `woo_variation_product_gallery_end`

= 1.2.2 =

* Fix: Issue when Jetpack Photon is in use
* Fix: Elementor Pro editor product image load
* Add: Support for [Duplicate Variations for WooCommerce](https://wordpress.org/plugins/variation-duplicator-for-woocommerce/)

= 1.2.1 =

* Add: Migration option from other plugin

= 1.2.0 =

* Add: More Gallery Configure options
* Add: CSS Clear fix option on mobile and table devices.

= 1.1.43 =

* Update: WP and WC Compatibility
* Add: Fixed width and height
* Add: Preloader disable option
* Fix: Selected attribute value images by URL


= 1.1.42 =

* Update: WP and WC Compatibility
* Fix: Empty gallery export PHP warning issue.

= 1.1.41 =

* Improve: Match variation change image

= 1.1.40 =

* Improve: Performance

= 1.1.39 =

* Improve: Gallery image attributes

= 1.1.38 =

* Fix: Gallery saving issue for WooCommerce 4.0 bug

= 1.1.37 =

* Add: Filter to modify featured image of gallery.
* Fix: affiliate-wp backend conflict issue.

= 1.1.36 =

* Fix: Divi theme load issue.

= 1.1.35 =

* Update: Gallery

= 1.1.34 =

* Update: Change settings load priority to fix WooCommerce Anti Fraud plugin issue.

= 1.1.33 =

* Fix: Known Issues

= 1.1.32 =

* Add: Basel Theme Support
* Add: Gallery data on WC REST API Response

= 1.1.31 =

* Update: WooCommerce template update

= 1.1.30 =

* Fix: WooCommerce REST Api issue

= 1.1.29 =

* Fix: Security update

= 1.1.28 =

* Fix: Source Map removed.

= 1.1.27 =

* Fix: Known Issue

= 1.1.26 =

* Add: Filter to override gallery template
* Fix: WooCommerce disable notice

= 1.1.25 =

* Update: Readme and Support WooCommerce 3.6

= 1.1.24 =

* Update: Settings Panel
* Add: Tutorial Section

= 1.1.23 =

* Add: Oxygen Theme Support

= 1.1.22 =

* Remove: CDN Load

= 1.1.21 =

* Add: Image Preload Settings

= 1.1.20 =

* Add: RTL Support
* Fix: Known JS Issue

= 1.1.19 =

* Fix: ROYAL Theme Issue
* Fix: Non image `srcset` issue

= 1.1.18 =

* Fix: Known JS Issue and Duplication

= 1.1.17 =

* Fix: Gallery Image loading ajax

= 1.1.16 =

* Fix: Bundle product disable
* Add: Customizr theme support

= 1.1.15 =

* Fix Shopkeeper and Saha theme template duplication issue.
* Add `wvg_product_images_template_include_once` filter added.

= 1.1.14 =

* Fix backend JS Issue
* Add YITH quickview support

= 1.1.13 =

* Fix JS Issue

= 1.1.12 =

* Add: Flatsome Custom product support.

= 1.1.11 =

* Add: Customify theme support
* Add: Suave theme support
* Add: `disable_wvg_inline_style` filter and `disable_wvg_enqueue_scripts` filter to prevent script loading.

= 1.1.10 =

* Add: IE-11 Support

= 1.1.9 =

* Add: Elementor pro support
* Add: Saha theme support
* Add: Preloader style option added
* Add: Gallery export-import added
* Add: Gallery responsive width option

= 1.1.8 =

* Fix: Backend JS Small Image Issue
* Add: Preload blur style

= 1.1.7 =

* Add: Preload variation images
* Add: Reset gallery option on select variation

= ******* =

* Fix: Small JS Issue.

= 1.1.6 =

* Fix: Salient Theme fix and single product gallery.

= 1.1.5 =

* Fix: Lazy loading on slow net.

= 1.1.4 =

* Add: Thumbnails Gap option
* Add: Support lots of theme default width

= 1.1.3 =

* Fix: Gallery Image loading issue

= 1.1.2 =

* Fix: Gallery Template Issue

= 1.1.1 =

* Fix: JS Issue

= 1.1.0 =

* Update: FlexSlider to Slick Slider
* Add: More Options

= 1.0.5 =

* Fix: Frontend CSS Update

= 1.0.4 =

* Add: Theme Override option added

= 1.0.3 =

* Fix: Non Gallery image issue

= 1.0.2 =

* **Kalium** Theme compatibility

= 1.0.1 =

* Duplicate feed fixing

= 1.0.0 =

* Initial release

== Upgrade Notice ==
